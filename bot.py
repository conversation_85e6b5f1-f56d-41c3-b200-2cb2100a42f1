import asyncio
from collections import defaultdict
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, <PERSON>
from aiogram.filters import Command
from aiogram.types import Message, CallbackQuery

from config import BOT_TOKEN
from db import init_models, Session, Channel, Post, count_posts
from keyboards import moderation_kb
import scheduler
from scheduler import publish_post
from parser import start_parser

bot = Bot(BOT_TOKEN, parse_mode="HTML")
dp = Dispatcher()
edit_state = defaultdict(int)        # admin_id -> post_id

@dp.message(Command("start"))
async def cmd_start(m: Message):
    await m.answer("✅ Бот запущен.\n/channels — список каналов.")

@dp.message(Command("channels"))
async def cmd_channels(m: Message):
    async with Session() as s:
        rows = (await s.execute(Channel.__table__.select())).scalars().all()
    if not rows:
        return await m.answer("Список пуст.")
    txt = "\n".join(f"{r.id}. @{r.username}" for r in rows)
    await m.answer(f"Мои каналы:\n{txt}")

@dp.message(Command("sources"))
async def cmd_sources(m: Message):
    parts = m.text.split()
    if len(parts) < 2:
        return await m.answer("Формат: /sources @channel")
    username = parts[1].lstrip("@")
    async with Session() as s:
        ch = await s.scalar(Channel.__table__.select().where(Channel.username == username))
        if not ch:
            return await m.answer("Канал не найден.")
        if not ch.sources:
            return await m.answer("Источников нет.")
        txt = "\n".join(f"• @{s.username}" for s in ch.sources)
    await m.answer(f"Источники @{username}:\n{txt}")

@dp.message(Command("stats"))
async def cmd_stats(m: Message):
    async with Session() as s:
        total = await count_posts(s)
        pub = await count_posts(s, posted=True)
        rej = await count_posts(s, rejected=True)
    await m.answer(f"Всего: {total}\nОпубликовано: {pub}\nОтклонено: {rej}")

@dp.callback_query(F.data.startswith("appr:"))
async def cb_appr(c: CallbackQuery):
    post_id = int(c.data.split(":")[1])
    async with Session() as s:
        post = await s.get(Post, post_id)
    if not post:
        return await c.answer("Не найдено.")
    await publish_post(post)
    await c.message.edit_reply_markup()
    await c.answer("✅ Опубликовано")

@dp.callback_query(F.data.startswith("rej:"))
async def cb_rej(c: CallbackQuery):
    post_id = int(c.data.split(":")[1])
    async with Session() as s:
        post = await s.get(Post, post_id)
        if post:
            post.rejected = True
            await s.commit()
    await c.message.edit_reply_markup()
    await c.answer("🚫 Отклонено")

@dp.callback_query(F.data.startswith("edit:"))
async def cb_edit(c: CallbackQuery):
    post_id = int(c.data.split(":")[1])
    edit_state[c.from_user.id] = post_id
    await c.answer("Пришлите новый текст.")

@dp.message()
async def handle_edit(m: Message):
    post_id = edit_state.pop(m.from_user.id, 0)
    if not post_id:
        return
    async with Session() as s:
        post = await s.get(Post, post_id)
        if not post:
            return await m.answer("Пост не найден.")
        post.gpt_text = m.text
        await s.commit()
        await m.answer("🔄 Обновлено.", reply_markup=moderation_kb(post.id, f"{post.target_id}"))

async def main():
    await init_models()
    scheduler.setup_scheduler()
    await asyncio.gather(
        dp.start_polling(bot, allowed_updates=dp.resolve_used_update_types()),
        start_parser(),
    )

if __name__ == "__main__":
    asyncio.run(main())
