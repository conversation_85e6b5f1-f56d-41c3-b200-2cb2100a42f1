# Telegram Bot для парсинга и репостинга контента

Этот проект представляет собой Telegram-бота для автоматического парсинга контента из источников, переписывания текстов с помощью ZeroEval API и публикации в целевые каналы с модерацией.

## Функциональность

- **Парсинг контента** из Telegram-каналов через Telethon
- **Переписывание текстов** через ZeroEval API (модель o3-2025-04-16)
- **Модерация постов** через inline-клавиатуры
- **Управление каналами и источниками** через команды
- **Статистика** публикаций
- **База данных** для хранения каналов, источников и постов

## Структура проекта

```
project/
│  bot.py           # Основной файл с Telegram ботом
│  parser.py        # Парсер сообщений через Telethon
│  gpt.py          # Интеграция с ZeroEval API
│  db.py           # Модели базы данных SQLAlchemy
│  scheduler.py    # Планировщик задач
│  keyboards.py    # Клавиатуры для модерации
│  config.py       # Конфигурация проекта
│  requirements.txt # Зависимости
│  .env            # Переменные окружения
└─ README.md       # Этот файл
```

## Установка и настройка

### 1. Клонирование и установка зависимостей

```powershell
# Создание виртуального окружения
python -m venv venv

# Активация виртуального окружения
.\venv\Scripts\Activate.ps1

# Установка зависимостей
pip install -r requirements.txt
```

### 2. Настройка переменных окружения

Скопируйте `.env.example` в `.env` и заполните своими данными:

```ini
# ----------- Telegram Bot -----------
BOT_TOKEN=ваш_токен_бота
ADMINS=ваш_telegram_id

# ----------- ZeroEval --------------
ZEROEVAL_API_KEY=ваш_zeroeval_api_key

# ----------- Telethon user client ---
API_ID=13434740
API_HASH=ddcb8777d24aa5a651a5bab7e17c585d
TG_SESSION=parser

# ----------- Database ---------------
DATABASE_URL=sqlite+aiosqlite:///data.db
```

### 3. Запуск

```powershell
python bot.py
```

При первом запуске Telethon попросит номер телефона и код подтверждения для создания сессии.

## Команды бота

- `/start` - Запуск бота
- `/channels` - Список каналов
- `/sources @channel` - Список источников для канала
- `/stats` - Статистика публикаций

## Модерация

Когда бот находит новый пост в источнике, он:
1. Переписывает текст через ZeroEval API
2. Отправляет админам для модерации
3. Предоставляет кнопки: "Выложить", "Редактировать", "Отклонить"

## Требования

- Python 3.11+
- Telegram Bot Token
- ZeroEval API Key
- Telegram API ID и Hash для Telethon

## Зависимости

- aiogram>=3.20.0 - Telegram Bot API
- telethon==1.34.0 - Telegram Client API
- SQLAlchemy>=2.0.41 - ORM для базы данных
- aiosqlite - Асинхронный SQLite драйвер
- python-dotenv - Загрузка переменных окружения
- apscheduler==3.10.4 - Планировщик задач
- openai>=1.82.0 - Клиент для ZeroEval API
