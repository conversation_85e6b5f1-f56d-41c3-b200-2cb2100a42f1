import asyncio
from typing import List

from telethon import TelegramClient, events
from aiogram import Bo<PERSON>

from config import BOT_TOKEN, ADMIN_IDS, API_ID, API_HASH, SESSION_NAME
from db import Session, Source, Channel, Post
from gpt import rewrite
from keyboards import moderation_kb

bot = Bot(BOT_TOKEN, parse_mode="HTML")
client = TelegramClient(SESSION_NAME, API_ID, API_HASH)

async def _load_source_ids() -> List[int]:
    async with Session() as s:
        rows = (await s.execute(Source.__table__.select())).fetchall()
        return [r.tg_id for r in rows]

async def start_parser():
    await client.start()      # при первом запуске спросит телефон / код

    source_ids = await _load_source_ids()

    @client.on(events.NewMessage(chats=source_ids))
    async def handler(event):
        async with Session() as db:
            # дубль
            exists = await db.scalar(
                Post.__table__.select().where(
                    Post.source_msg == event.id,
                    Post.source_id == event.chat_id,
                )
            )
            if exists:
                return

            src: Source | None = await db.scalar(
                Source.__table__.select().where(Source.tg_id == event.chat_id)
            )
            if not src:
                return
            ch: Channel | None = await db.get(Channel, src.channel_id)
            if not ch:
                return

            text = event.message.message or ""
            # фильтры
            if src.filters:
                kw = [k.strip().lower() for k in src.filters.split(",")]
                if any(k in text.lower() for k in kw):
                    return

            gpt_text = await rewrite(text, ch.template, ch.style)

            post = Post(
                source_msg=event.id,
                source_id=event.chat_id,
                target_id=ch.tg_id,
                text=text,
                media_type="text",
                gpt_text=gpt_text,
            )
            db.add(post)
            await db.commit()
            await db.refresh(post)

            for admin in ADMIN_IDS:
                await bot.send_message(
                    admin,
                    gpt_text,
                    reply_markup=moderation_kb(post.id, ch.username),
                )

    await client.run_until_disconnected()
