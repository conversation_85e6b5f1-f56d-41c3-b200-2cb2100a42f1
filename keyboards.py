from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

def moderation_kb(post_id: int, target_username: str) -> InlineKeyboardMarkup:
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(f"✅ Выложить в @{target_username}", callback_data=f"appr:{post_id}")],
            [InlineKeyboardButton("📝 Редактировать", callback_data=f"edit:{post_id}")],
            [InlineKeyboardButton("❌ Отклонить", callback_data=f"rej:{post_id}")],
        ]
    )
