# 🚀 Быстрый старт

## 1. Проверка готовности

Запустите тестовый скрипт:
```powershell
.\venv\Scripts\python.exe test_setup.py
```

Если все проверки прошли успешно, переходите к следующему шагу.

## 2. Настройка каналов и источников

Запустите интерактивный скрипт настройки:
```powershell
.\venv\Scripts\python.exe setup_channels.py
```

Выберите пункт "1" для создания примеров или добавьте свои каналы и источники.

## 3. Получение ID каналов

Для получения ID Telegram каналов:

1. **Для публичных каналов**: используйте @userinfobot
2. **Для приватных каналов**: 
   - Добавьте бота в канал как администратора
   - Отправьте любое сообщение в канал
   - ID будет в логах бота

## 4. Запуск бота

```powershell
.\venv\Scripts\python.exe bot.py
```

При первом запуске Telethon попросит:
- Номер телефона
- Код подтверждения из Telegram

## 5. Проверка работы

1. Отправьте боту команду `/start`
2. Проверьте список каналов: `/channels`
3. Проверьте источники: `/sources @your_channel`

## 6. Настройка прав

Убедитесь, что:
- Бот добавлен в целевые каналы как администратор
- У бота есть права на отправку сообщений
- Telethon клиент имеет доступ к источникам

## Команды бота

- `/start` - Запуск бота
- `/channels` - Список каналов  
- `/sources @channel` - Источники канала
- `/stats` - Статистика

## Модерация

Когда бот найдет новый пост:
1. Переписывает через ZeroEval API
2. Отправляет админам с кнопками:
   - ✅ Выложить
   - 📝 Редактировать  
   - ❌ Отклонить

## Устранение проблем

### Ошибки авторизации Telethon
```powershell
# Удалите файл сессии и авторизуйтесь заново
del parser.session
```

### Ошибки ZeroEval API
- Проверьте API ключ в .env
- Убедитесь, что у вас есть кредиты

### Ошибки Telegram Bot
- Проверьте токен бота в .env
- Убедитесь, что бот не заблокирован

## Логи

Бот выводит подробные логи в консоль. При возникновении ошибок проверьте вывод.
