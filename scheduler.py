from apscheduler.schedulers.asyncio import AsyncIOScheduler
from aiogram import <PERSON><PERSON>
from config import BOT_TOKEN
from db import Session, Post

bot = Bot(BOT_TOKEN, parse_mode="HTML")
sched = AsyncIOScheduler()

async def publish_post(post: Post):
    msg = await bot.send_message(post.target_id, post.gpt_text)
    async with Session() as s:
        post.posted = True
        post.target_msg = msg.message_id
        await s.merge(post)
        await s.commit()

def setup_scheduler():
    sched.start()
