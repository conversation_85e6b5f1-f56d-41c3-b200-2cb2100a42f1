import os
from dotenv import load_dotenv

load_dotenv()

# --- <PERSON><PERSON>ram <PERSON>t ---
BOT_TOKEN = os.getenv("BOT_TOKEN")

# --- Admins ---
ADMIN_IDS = {int(x) for x in os.getenv("ADMINS", "").split(",") if x}

# --- ZeroEval / OpenAI proxy ---
ZEROEVAL_KEY = os.getenv("ZEROEVAL_API_KEY")

# --- Telethon user-client ---
API_ID   = int(os.getenv("API_ID", "13434740"))
API_HASH = os.getenv("API_HASH", "ddcb8777d24aa5a651a5bab7e17c585d")
SESSION_NAME = os.getenv("TG_SESSION", "parser")     # создастся файл parser.session

# --- Database ---
DB_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///data.db")
