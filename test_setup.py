#!/usr/bin/env python3
"""
Тестовый скрипт для проверки настройки проекта
"""
import asyncio
import sys
from config import BOT_TOKEN, ZEROEVAL_KEY, ADMIN_IDS, API_ID, API_HASH

async def test_setup():
    print("🔍 Проверка настройки проекта...")
    
    # Проверка переменных окружения
    print("\n📋 Проверка переменных окружения:")
    
    if BOT_TOKEN:
        print("✅ BOT_TOKEN настроен")
    else:
        print("❌ BOT_TOKEN не настроен")
        return False
    
    if ZEROEVAL_KEY:
        print("✅ ZEROEVAL_API_KEY настроен")
    else:
        print("❌ ZEROEVAL_API_KEY не настроен")
        return False
    
    if ADMIN_IDS:
        print(f"✅ ADMINS настроены: {len(ADMIN_IDS)} админов")
    else:
        print("❌ ADMINS не настроены")
        return False
    
    if API_ID and API_HASH:
        print("✅ Telethon API_ID и API_HASH настроены")
    else:
        print("❌ Telethon API_ID или API_HASH не настроены")
        return False
    
    # Проверка импортов
    print("\n📦 Проверка импортов:")
    
    try:
        from db import init_models, Session
        print("✅ База данных (SQLAlchemy)")
    except Exception as e:
        print(f"❌ База данных: {e}")
        return False
    
    try:
        from gpt import rewrite
        print("✅ GPT модуль (OpenAI)")
    except Exception as e:
        print(f"❌ GPT модуль: {e}")
        return False
    
    try:
        from aiogram import Bot
        print("✅ Aiogram")
    except Exception as e:
        print(f"❌ Aiogram: {e}")
        return False
    
    try:
        from telethon import TelegramClient
        print("✅ Telethon")
    except Exception as e:
        print(f"❌ Telethon: {e}")
        return False
    
    # Проверка инициализации базы данных
    print("\n🗄️ Проверка базы данных:")
    try:
        await init_models()
        print("✅ База данных инициализирована")
    except Exception as e:
        print(f"❌ Ошибка инициализации БД: {e}")
        return False
    
    print("\n🎉 Все проверки пройдены! Проект готов к запуску.")
    print("\n📝 Для запуска бота выполните:")
    print("   python bot.py")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_setup())
    sys.exit(0 if success else 1)
