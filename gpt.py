"""
Асинхронный вызов LLM через ZeroEval-прокси.
"""
import asyncio
from openai import OpenAI
from config import ZEROEVAL_KEY

client = OpenAI(
    base_url="https://api.zeroeval.com/proxy/chat/completions",
    api_key=ZEROEVAL_KEY,
)

MODEL_NAME = "o3-2025-04-16"     # можете заменить при необходимости

def _rewrite_sync(text: str, tpl: str, style: str) -> str:
    prompt = f"""
Ты копирайтер. Перепиши текст в стиле "{style}", сделай уникальным.
Удали ссылки, @ники, рекламу доноров.
В начало вставь заголовок/хэштег, в конец — рекламную строку из шаблона.

Шаблон:
{tpl}

Исходный текст:
{text}

Отдай только финальный пост.
"""
    resp = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[{"role": "user", "content": prompt}],
        temperature=0.6,
    )
    return resp.choices[0].message.content.strip()

async def rewrite(text: str, tpl: str, style: str) -> str:
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, _rewrite_sync, text, tpl, style)
