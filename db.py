import datetime as dt
import typing as t

from sqlalchemy import (
    String, Integer, Text, Boolean,
    ForeignKey, DateTime, select, func
)
from sqlalchemy.ext.asyncio import (
    create_async_engine, async_sessionmaker, AsyncSession
)
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from config import DB_URL

engine = create_async_engine(DB_URL, echo=False, future=True)
Session: async_sessionmaker[AsyncSession] = async_sessionmaker(
    engine, expire_on_commit=False
)

class Base(DeclarativeBase):
    pass

class Channel(Base):
    __tablename__ = "channels"
    id:        Mapped[int]  = mapped_column(primary_key=True)
    tg_id:     Mapped[int]  = mapped_column(unique=True)
    username:  Mapped[str]  = mapped_column(String(64))
    template:  Mapped[str]  = mapped_column(Text, default="")
    style:     Mapped[str]  = mapped_column(String(16), default="serious")
    sources:   Mapped[t.List["Source"]] = relationship(back_populates="channel")

class Source(Base):
    __tablename__ = "sources"
    id:         Mapped[int] = mapped_column(primary_key=True)
    channel_id: Mapped[int] = mapped_column(ForeignKey("channels.id"))
    tg_id:      Mapped[int] = mapped_column(Integer)
    username:   Mapped[str] = mapped_column(String(64))
    filters:    Mapped[str] = mapped_column(Text, default="")
    channel:    Mapped[Channel] = relationship(back_populates="sources")

class Post(Base):
    __tablename__ = "posts"
    id:          Mapped[int]  = mapped_column(primary_key=True)
    source_msg:  Mapped[int]
    source_id:   Mapped[int]
    target_id:   Mapped[int]
    text:        Mapped[str]  = mapped_column(Text)
    media_type:  Mapped[str]  = mapped_column(String(16))
    gpt_text:    Mapped[str]  = mapped_column(Text)
    posted:      Mapped[bool] = mapped_column(Boolean, default=False)
    rejected:    Mapped[bool] = mapped_column(Boolean, default=False)
    target_msg:  Mapped[int | None]
    created:     Mapped[dt.datetime] = mapped_column(
        DateTime, default=dt.datetime.utcnow
    )

async def init_models():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

# маленький хелпер для статистики
async def count_posts(session: AsyncSession, **where) -> int:
    stmt = select(func.count(Post.id))
    for k, v in where.items():
        stmt = stmt.where(getattr(Post, k) == v)
    return (await session.scalar(stmt)) or 0
