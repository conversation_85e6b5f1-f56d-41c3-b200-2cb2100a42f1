#!/usr/bin/env python3
"""
Скрипт для настройки каналов и источников
"""
import asyncio
from db import Session, Channel, Source

async def setup_example_data():
    """Создает примеры каналов и источников для демонстрации"""
    
    async with Session() as session:
        # Проверяем, есть ли уже данные
        existing_channels = await session.execute(Channel.__table__.select())
        if existing_channels.fetchall():
            print("📋 Каналы уже настроены. Пропускаем создание примеров.")
            return
        
        print("🔧 Создание примеров каналов и источников...")
        
        # Создаем пример канала
        channel = Channel(
            tg_id=-1001234567890,  # Замените на реальный ID вашего канала
            username="my_channel",  # Замените на username вашего канала
            template="🔥 Подписывайтесь на @my_channel для получения актуальных новостей!",
            style="serious"  # или "casual", "funny", etc.
        )
        session.add(channel)
        await session.flush()  # Получаем ID канала
        
        # Создаем пример источника
        source = Source(
            channel_id=channel.id,
            tg_id=-1001987654321,  # Замените на реальный ID источника
            username="news_source",  # Замените на username источника
            filters="реклама,спам,промо"  # Фильтры для исключения постов
        )
        session.add(source)
        
        await session.commit()
        
        print("✅ Примеры созданы!")
        print(f"   Канал: @{channel.username} (ID: {channel.tg_id})")
        print(f"   Источник: @{source.username} (ID: {source.tg_id})")
        print("\n⚠️  ВАЖНО: Замените ID и username на реальные значения!")

async def add_channel():
    """Интерактивное добавление канала"""
    print("\n➕ Добавление нового канала")
    
    try:
        tg_id = int(input("Введите Telegram ID канала (например, -1001234567890): "))
        username = input("Введите username канала (без @): ")
        template = input("Введите рекламный шаблон (будет добавляться в конец постов): ")
        style = input("Введите стиль переписывания (serious/casual/funny): ") or "serious"
        
        async with Session() as session:
            channel = Channel(
                tg_id=tg_id,
                username=username,
                template=template,
                style=style
            )
            session.add(channel)
            await session.commit()
            await session.refresh(channel)
            
            print(f"✅ Канал @{username} добавлен с ID {channel.id}")
            
    except ValueError:
        print("❌ Ошибка: ID должен быть числом")
    except Exception as e:
        print(f"❌ Ошибка: {e}")

async def add_source():
    """Интерактивное добавление источника"""
    print("\n➕ Добавление нового источника")
    
    # Показываем доступные каналы
    async with Session() as session:
        channels = (await session.execute(Channel.__table__.select())).fetchall()
        
        if not channels:
            print("❌ Сначала создайте канал!")
            return
        
        print("Доступные каналы:")
        for ch in channels:
            print(f"  {ch.id}. @{ch.username}")
    
    try:
        channel_id = int(input("Введите ID канала: "))
        tg_id = int(input("Введите Telegram ID источника: "))
        username = input("Введите username источника (без @): ")
        filters = input("Введите фильтры через запятую (необязательно): ") or ""
        
        async with Session() as session:
            source = Source(
                channel_id=channel_id,
                tg_id=tg_id,
                username=username,
                filters=filters
            )
            session.add(source)
            await session.commit()
            
            print(f"✅ Источник @{username} добавлен")
            
    except ValueError:
        print("❌ Ошибка: ID должны быть числами")
    except Exception as e:
        print(f"❌ Ошибка: {e}")

async def list_channels():
    """Показать все каналы и источники"""
    async with Session() as session:
        channels = (await session.execute(Channel.__table__.select())).fetchall()
        
        if not channels:
            print("📋 Каналы не настроены")
            return
        
        print("\n📋 Настроенные каналы:")
        for ch in channels:
            print(f"\n🔸 {ch.id}. @{ch.username} (ID: {ch.tg_id})")
            print(f"   Стиль: {ch.style}")
            print(f"   Шаблон: {ch.template[:50]}...")
            
            # Показываем источники для этого канала
            sources = await session.execute(
                Source.__table__.select().where(Source.channel_id == ch.id)
            )
            sources = sources.fetchall()
            
            if sources:
                print("   Источники:")
                for src in sources:
                    filters_text = f" (фильтры: {src.filters})" if src.filters else ""
                    print(f"     • @{src.username} (ID: {src.tg_id}){filters_text}")
            else:
                print("   Источники: не настроены")

async def main():
    print("🤖 Настройка каналов и источников для Telegram бота")
    print("=" * 50)
    
    while True:
        print("\nВыберите действие:")
        print("1. Создать примеры данных")
        print("2. Добавить канал")
        print("3. Добавить источник")
        print("4. Показать все каналы")
        print("0. Выход")
        
        choice = input("\nВведите номер: ").strip()
        
        if choice == "1":
            await setup_example_data()
        elif choice == "2":
            await add_channel()
        elif choice == "3":
            await add_source()
        elif choice == "4":
            await list_channels()
        elif choice == "0":
            print("👋 До свидания!")
            break
        else:
            print("❌ Неверный выбор")

if __name__ == "__main__":
    asyncio.run(main())
